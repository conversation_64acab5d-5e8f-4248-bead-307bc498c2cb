// Simple test script to verify obfuscation is working
const JavaScriptObfuscator = require('./src/obfuscator');

const testCode = `
function calculateSum(a, b) {
    const result = a + b;
    console.log("The sum is: " + result);
    return result;
}

const numbers = [1, 2, 3, 4, 5];
const total = numbers.reduce(calculateSum, 0);
console.log("Total:", total);
`;

console.log('🧪 Testing JavaScript Obfuscator...\n');

try {
    const obfuscator = new JavaScriptObfuscator();
    const obfuscatedCode = obfuscator.obfuscate(testCode);
    
    console.log('✅ Original Code:');
    console.log(testCode);
    
    console.log('\n🔐 Obfuscated Code:');
    console.log(obfuscatedCode);
    
    console.log('\n✅ Obfuscation completed successfully!');
    
} catch (error) {
    console.error('❌ Obfuscation failed:', error.message);
    process.exit(1);
}
