// Debug test to identify the issue
const { generateRandomName } = require('./src/utils');

console.log('Testing random name generation...');

for (let i = 0; i < 10; i++) {
    const name = generateRandomName();
    console.log(`Generated name ${i + 1}: "${name}" (length: ${name.length})`);
}

console.log('\nTesting Babel parsing...');

const { parse } = require('@babel/parser');
const generate = require('@babel/generator').default;

const simpleCode = 'function test() { return 42; }';

try {
    const ast = parse(simpleCode, { sourceType: 'script' });
    console.log('✅ Parsing successful');
    
    const result = generate(ast);
    console.log('✅ Generation successful');
    console.log('Generated code:', result.code);
    
} catch (error) {
    console.error('❌ Babel error:', error.message);
}
