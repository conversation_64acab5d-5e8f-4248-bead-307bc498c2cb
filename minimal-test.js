// Minimal test to isolate the issue
const { parse } = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');

const simpleCode = 'function test() { return 42; }';

console.log('Testing minimal obfuscation...');

try {
    const ast = parse(simpleCode, { sourceType: 'script' });
    console.log('✅ Parsing successful');
    
    // Simple transformation - just rename function
    traverse(ast, {
        FunctionDeclaration: (path) => {
            if (path.node.id && t.isIdentifier(path.node.id)) {
                path.node.id.name = '_obfuscated';
            }
        }
    });
    
    console.log('✅ Transformation successful');
    
    const result = generate(ast, { compact: true });
    console.log('✅ Generation successful');
    console.log('Result:', result.code);
    
} catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
}
