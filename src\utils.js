function generateRandomName(length = Math.floor(Math.random() * 6) + 5) {
    const chars = ['_','$']
    const chars2 = ["̖", "̗", "̘", "̙", "̜", "̝", "̞", "̟", "̠", "̤", "̥", "̦", "̩", "̪", "̫", "̬", "̭", "̮", "̯", "̰", "̱", "̲", "̳", "̹", "̺", "̻", "̼", "ͅ", "͇", "͈", "͉", "͍", "͎", "͓", "͔", "͕", "͖", "͙", "͚", "̣","ู","ุ"];
    let result = '';

    for (let i = 0; i < length; i++) {
        result += chars[Math.floor(Math.random() * chars.length)];
        result += chars2[Math.floor(Math.random() * chars2.length)];
    }

    return result;
}